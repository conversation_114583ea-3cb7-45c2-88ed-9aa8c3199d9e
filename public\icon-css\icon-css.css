@font-face {
  font-family: 'das';
  src:
    url('/fonts/das.ttf?6lvbnu') format('truetype'),
    url('/fonts/das.woff?6lvbnu') format('woff'),
    url('/fonts/das.svg?6lvbnu#das') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="das-"], [class*=" das-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'das' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.das-video-call-cancel:before {
  content: "\e908";
}
.das-unmute:before {
  content: "\e900";
}
.das-call:before {
  content: "\e901";
}
.das-mute:before {
  content: "\e902";
}
.das-speaker:before {
  content: "\e903";
}
.das-messag:before {
  content: "\e904";
}
.das-video-call:before {
  content: "\e905";
}
.das-video-switch:before {
  content: "\e906";
}
.das-left-icon:before {
  content: "\e907";
}
