import React, { useState, useRef, useEffect } from 'react';
import {
  VideoCameraIcon,
  VideoCameraSlashIcon,
  MicrophoneIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon,
  Cog6ToothIcon,
  PhoneXMarkIcon,
  ArrowLeftIcon, ArrowPathIcon
} from "@heroicons/react/24/solid";
import {
  CallClient,
  CallAgent,
  LocalVideoStream,
  VideoStreamRenderer,
  Call,
  RemoteParticipant,
  RemoteVideoStream
} from '@azure/communication-calling';
import { AzureCommunicationTokenCredential } from '@azure/communication-common';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type FormData = {
  userId: string;
  accessToken: string;
  groupId: string;
};

type CallState = {
  isConnected: boolean;
  isConnecting: boolean;
  isCameraOn: boolean;
  isMicOn: boolean;
  isSpeakerOn: boolean;
  selectedSpeakers: string[];
};

type DeviceInfo = {
  id: string;
  name: string;
};

const VideoCallApp = () => {
  const [formData, setFormData] = useState<FormData>({
    userId: '',
    accessToken: '',
    groupId: ''
  });

  const [callState, setCallState] = useState<CallState>({
    isConnected: false,
    isConnecting: false,
    isCameraOn: true,
    isMicOn: true,
    isSpeakerOn: true,
    selectedSpeakers: []
  });

  const [localPosition, setLocalPosition] = useState({ top: 20, left: 20 });

  // Device selection states
  const [microphones, setMicrophones] = useState<DeviceInfo[]>([]);
  const [speakers, setSpeakers] = useState<DeviceInfo[]>([]);
  const [cameras, setCameras] = useState<DeviceInfo[]>([]);
  const [selectedMicrophoneId, setSelectedMicrophoneId] = useState<string>('');
  const [selectedSpeakerId, setSelectedSpeakerId] = useState<string>('');
  const [selectedCameraId, setSelectedCameraId] = useState<string>('');
  const [showDeviceSettings, setShowDeviceSettings] = useState<boolean>(false);

  const callClientRef = useRef<CallClient | null>(null);
  const callAgentRef = useRef<CallAgent | null>(null);
  const currentCallRef = useRef<Call | null>(null);
  const localVideoStreamRef = useRef<LocalVideoStream | null>(null);
  const localRendererRef = useRef<VideoStreamRenderer | null>(null);
  const remoteRendererRef = useRef<VideoStreamRenderer | null>(null);
  const remoteVideoRef = useRef<HTMLDivElement | null>(null);
  const localVideoRef = useRef<HTMLDivElement | null>(null);
  const deviceManagerRef = useRef<any>(null);
  const audioObserverRef = useRef<MutationObserver | null>(null);

  useEffect(() => {
    return () => {
      disconnectCall();
      // Clean up the audio observer if it exists
      if (audioObserverRef.current) {
        audioObserverRef.current.disconnect();
      }
    };
  }, []);

  // Set up an observer to watch for new audio elements and apply mute state
  useEffect(() => {
    // Function to apply mute state to audio elements
    const applyMuteState = () => {
      const audioElements = document.querySelectorAll('audio');
      audioElements.forEach(audio => {
        audio.muted = !callState.isSpeakerOn;
      });
    };

    // Set up a mutation observer to watch for new audio elements
    if (callState.isConnected) {
      // First, apply to any existing audio elements
      applyMuteState();

      // Then set up observer for future elements
      const observer = new MutationObserver((mutations) => {
        let shouldCheck = false;

        mutations.forEach(mutation => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Check if any of the added nodes are audio elements or contain audio elements
            mutation.addedNodes.forEach(node => {
              if (node.nodeType === 1) { // Element node
                if ((node as Element).tagName === 'AUDIO' || (node as Element).querySelector('audio')) {
                  shouldCheck = true;
                }
              }
            });
          }
        });

        if (shouldCheck) {
          applyMuteState();
        }
      });

      // Start observing the document with the configured parameters
      observer.observe(document.body, { childList: true, subtree: true });
      audioObserverRef.current = observer;

      return () => {
        observer.disconnect();
        audioObserverRef.current = null;
      };
    }
  }, [callState.isConnected, callState.isSpeakerOn]);

  const initializeCallClient = async (formData) => {
    try {
      const tokenCredential = new AzureCommunicationTokenCredential(formData.accessToken);
      const callClient = new CallClient();
      const callAgent = await callClient.createCallAgent(tokenCredential, {
        displayName: formData.userId
      });

      // Store the device manager for later use with speaker control
      deviceManagerRef.current = await callClient.getDeviceManager();

      // Get available audio devices
      await getAudioDevices();
      // Get available cameras
      await getCameraDevices();

      callClientRef.current = callClient;
      callAgentRef.current = callAgent;
      return true;
    } catch (err) {
      console.error("Failed to initialize call client:", err);
      return false;
    }
  };

  // Get available audio devices
  const getAudioDevices = async () => {
    if (!deviceManagerRef.current) return;

    try {
      // Get microphones
      const mics = await deviceManagerRef.current.getMicrophones();
      const micDevices = mics.map((mic: any) => ({
        id: mic.id,
        name: mic.name || `Microphone (${mic.id.slice(0, 8)})`
      }));
      setMicrophones(micDevices);

      if (micDevices.length > 0) {
        setSelectedMicrophoneId(micDevices[0].id);
      }

      // Get speakers
      const speakerDevices = await deviceManagerRef.current.getSpeakers();
      const speakers = speakerDevices.map((speaker: any) => ({
        id: speaker.id,
        name: speaker.name || `Speaker (${speaker.id.slice(0, 8)})`
      }));
      setSpeakers(speakers);

      if (speakers.length > 0) {
        setSelectedSpeakerId(speakers[0].id);
      }
    } catch (err) {
      console.error("Error getting audio devices:", err);
    }
  };

  // Get available camera devices
  const getCameraDevices = async () => {
    if (!deviceManagerRef.current) return;

    try {
      const cameraDevices = await deviceManagerRef.current.getCameras();
      const camerasList = cameraDevices.map((camera: any) => {
        // Try to identify front/back cameras on mobile
        let name = camera.name || `Camera (${camera.id.slice(0, 8)})`;
        if (name.toLowerCase().includes('front')) {
          name = `Front Camera (${camera.id.slice(0, 8)})`;
        } else if (name.toLowerCase().includes('back') || name.toLowerCase().includes('rear')) {
          name = `Back Camera (${camera.id.slice(0, 8)})`;
        } else if (/user/i.test(camera.id)) {
          name = `Front Camera (${camera.id.slice(0, 8)})`;
        } else if (/environment/i.test(camera.id)) {
          name = `Back Camera (${camera.id.slice(0, 8)})`;
        }

        return {
          id: camera.id,
          name: name
        };
      });

      setCameras(camerasList);

      if (camerasList.length > 0) {
        setSelectedCameraId(camerasList[0].id);
      }
    } catch (err) {
      console.error("Error getting camera devices:", err);
    }
  };

  // Change microphone
  const changeMicrophone = async (deviceId: string) => {
    if (!deviceManagerRef.current) return;

    try {
      const mics = await deviceManagerRef.current.getMicrophones();
      const selectedMic = mics.find((mic: any) => mic.id === deviceId);

      if (selectedMic) {
        await deviceManagerRef.current.selectMicrophone(selectedMic);
        setSelectedMicrophoneId(deviceId);
        console.log(`Microphone changed to ${selectedMic.name || 'selected device'}`);
      }
    } catch (err) {
      console.error("Error changing microphone:", err);
    }
  };

  // Change speaker - modified to allow only one speaker at a time
  const changeSpeaker = async (deviceId: string) => {
    if (!deviceManagerRef.current) return;

    try {
      const speakerDevices = await deviceManagerRef.current.getSpeakers();

      // Find the selected speaker device
      const selectedSpeaker = speakerDevices.find((s: any) => s.id === deviceId);

      if (selectedSpeaker) {
        // Update the device manager with the selected speaker
        await deviceManagerRef.current.selectSpeaker(selectedSpeaker);

        // Update the UI state
        setSelectedSpeakerId(deviceId);

        // Update the selected speakers array with just this one speaker
        setCallState(s => ({
          ...s,
          selectedSpeakers: [deviceId],
          isSpeakerOn: true // Also ensure speaker is turned on when a device is selected
        }));

        console.log(`Speaker changed to ${selectedSpeaker.name || 'selected device'}`);
      }
    } catch (err) {
      console.error("Error changing speaker:", err);
    }
  };

  // Change camera
  const changeCamera = async (deviceId: string) => {
    if (!deviceManagerRef.current || !callState.isCameraOn) return;

    try {
      const cameraDevices = await deviceManagerRef.current.getCameras();
      const selectedCamera = cameraDevices.find((camera: any) => camera.id === deviceId);

      if (selectedCamera) {
        setSelectedCameraId(deviceId);

        // If we're in a call and have a video stream, we need to switch the camera
        if (currentCallRef.current && localVideoStreamRef.current) {
          // Stop the current video stream
          await currentCallRef.current.stopVideo(localVideoStreamRef.current);

          // Dispose of the current renderer
          if (localRendererRef.current) {
            localRendererRef.current.dispose();
            localRendererRef.current = null;
          }

          // Create a new local video stream with the selected camera
          const newLocalStream = new LocalVideoStream(selectedCamera);
          localVideoStreamRef.current = newLocalStream;

          // Start video with the new stream
          await currentCallRef.current.startVideo(newLocalStream);

          // Create a new renderer and view
          const renderer = new VideoStreamRenderer(newLocalStream);
          localRendererRef.current = renderer;
          const view = await renderer.createView();

          if (localVideoRef.current) {
            localVideoRef.current.innerHTML = '';
            localVideoRef.current.appendChild(view.target);
          }

          console.log(`Camera changed to ${selectedCamera.name || 'selected device'}`);
        } else {
          console.log(`Camera will be used when video is enabled`);
        }
      }
    } catch (err) {
      console.error("Error changing camera:", err);
    }
  };

  const startLocalVideo = async () => {
    if (!deviceManagerRef.current || !callState.isConnected) return;

    try {
      const devices = await deviceManagerRef.current.getCameras();

      // Use the selected camera if available, otherwise use the first camera
      let camera;
      if (selectedCameraId) {
        camera = devices.find((cam: any) => cam.id === selectedCameraId);
      }

      if (!camera && devices.length > 0) {
        camera = devices[0];
        setSelectedCameraId(camera.id);
      }

      if (!camera) {
        console.error("No camera found.");
        return;
      }

      const localStream = new LocalVideoStream(camera);
      localVideoStreamRef.current = localStream;

      const renderer = new VideoStreamRenderer(localStream);
      localRendererRef.current = renderer;
      const view = await renderer.createView();

      if (localVideoRef.current) {
        localVideoRef.current.innerHTML = '';
        localVideoRef.current.appendChild(view.target);
      }
    } catch (err) {
      console.error("Error starting local video:", err);
    }
  };

  const connectCall = async (formData) => {
    if (!formData.userId || !formData.accessToken || !formData.groupId) {
      console.warn("Missing Fields: Please enter all fields.");
      return;
    }

    setCallState(s => ({ ...s, isConnecting: true }));
    const ok = await initializeCallClient(formData);
    if (!ok) return setCallState(s => ({ ...s, isConnecting: false }));

    // Initialize local video stream for use in the call, but don't render it yet
    if (callState.isCameraOn) {
      try {
        const devices = await deviceManagerRef.current?.getCameras();

        // Use the selected camera if available, otherwise use the first camera
        let camera;
        if (selectedCameraId) {
          camera = devices?.find((cam: any) => cam.id === selectedCameraId);
        }

        if (!camera && devices && devices.length > 0) {
          camera = devices[0];
          setSelectedCameraId(camera.id);
        }

        if (camera) {
          const localStream = new LocalVideoStream(camera);
          localVideoStreamRef.current = localStream;
        }
      } catch (err) {
        console.error("Error initializing local video stream:", err);
      }
    }

    const call = await callAgentRef.current?.join(
      { roomId: formData.groupId },
      {
        videoOptions: {
          localVideoStreams: localVideoStreamRef.current ? [localVideoStreamRef.current] : []
        },
        audioOptions: {
          muted: !callState.isMicOn
        }
      }
    );

    if (!call) return;

    currentCallRef.current = call;

    const handleStream = async (stream: RemoteVideoStream) => {
      if (stream.isAvailable) {
        await renderRemoteVideoStream(stream);
      } else {
        stream.on('isAvailableChanged', async () => {
          if (stream.isAvailable) await renderRemoteVideoStream(stream);
          else clearRemoteVideo();
        });
      }
    };

    const subscribeToParticipant = (participant: RemoteParticipant) => {
      participant.videoStreams.forEach(handleStream);
      participant.on('videoStreamsUpdated', e => {
        e.added.forEach(handleStream);
        e.removed.forEach(() => clearRemoteVideo());
      });
    };

    call.remoteParticipants.forEach(subscribeToParticipant);

    call.on('remoteParticipantsUpdated', e => {
      e.added.forEach(subscribeToParticipant);
      e.removed.forEach(() => clearRemoteVideo());
    });

    setCallState(s => ({
      ...s,
      isConnected: true,
      isConnecting: false
    }));

    // Now that the call is connected, render the local video
    if (callState.isCameraOn && localVideoStreamRef.current) {
      try {
        const renderer = new VideoStreamRenderer(localVideoStreamRef.current);
        localRendererRef.current = renderer;
        const view = await renderer.createView();

        if (localVideoRef.current) {
          localVideoRef.current.innerHTML = '';
          localVideoRef.current.appendChild(view.target);
        }
      } catch (err) {
        console.error("Error rendering local video after connection:", err);
      }
    }

    console.log("Connected: Call started.");
  };

  const renderRemoteVideoStream = async (stream: RemoteVideoStream) => {
    clearRemoteVideo();
    const renderer = new VideoStreamRenderer(stream);
    remoteRendererRef.current = renderer;

    try {
      const view = await renderer.createView();
      if (remoteVideoRef.current) {
        remoteVideoRef.current.innerHTML = '';
        remoteVideoRef.current.appendChild(view.target);
      }
    } catch (err) {
      console.error("Error rendering remote stream", err);
      renderer.dispose();
    }
  };

  const clearRemoteVideo = () => {
    if (remoteRendererRef.current) {
      remoteRendererRef.current.dispose();
      remoteRendererRef.current = null;
    }
    if (remoteVideoRef.current) {
      remoteVideoRef.current.innerHTML = '';
    }
  };

  const disconnectCall = async () => {
    try {
      await currentCallRef.current?.hangUp();
    } catch (e) {
      console.error(e);
    }

    if (localRendererRef.current) {
      localRendererRef.current.dispose();
      localRendererRef.current = null;
    }

    if (localVideoStreamRef.current) {
      localVideoStreamRef.current = null;
    }

    clearRemoteVideo();
    currentCallRef.current = null;

    // Reset all state
    setCallState({
      isConnected: false,
      isConnecting: false,
      isCameraOn: true,
      isMicOn: true,
      isSpeakerOn: true,
      selectedSpeakers: []
    });

    // Reset selected device IDs
    setSelectedMicrophoneId('');
    setSelectedSpeakerId('');
    setSelectedCameraId('');

    console.log("Disconnected: Call ended.");
  };

  const toggleCamera = async () => {
    if (!currentCallRef.current || !callState.isConnected) return;

    if (callState.isCameraOn) {
      await currentCallRef.current.stopVideo(localVideoStreamRef.current);
      localRendererRef.current?.dispose();
      localRendererRef.current = null;
    } else {
      await currentCallRef.current.startVideo(localVideoStreamRef.current);
      const renderer = new VideoStreamRenderer(localVideoStreamRef.current);
      localRendererRef.current = renderer;
      const view = await renderer.createView();
      if (localVideoRef.current) {
        localVideoRef.current.innerHTML = '';
        localVideoRef.current.appendChild(view.target);
      }
    }

    setCallState(s => ({ ...s, isCameraOn: !s.isCameraOn }));
  };

  const toggleMic = async () => {
    if (!currentCallRef.current) return;
    if (callState.isMicOn) await currentCallRef.current.mute();
    else await currentCallRef.current.unmute();
    setCallState(s => ({ ...s, isMicOn: !s.isMicOn }));
  };

  // Toggle speaker - modified to handle multiple speakers
  const toggleSpeaker = async () => {
    if (!deviceManagerRef.current) return;

    try {
      // Toggle the speaker state in our UI
      const newSpeakerState = !callState.isSpeakerOn;
      setCallState(s => ({ ...s, isSpeakerOn: newSpeakerState }));

      // Get all audio elements on the page
      const audioElements = document.querySelectorAll('audio');

      // Set the muted property on all audio elements
      audioElements.forEach(audio => {
        audio.muted = !newSpeakerState;
      });

      // If there are no audio elements yet, we'll set up an observer to catch them when they're created
      if (audioElements.length === 0) {
        // Create a hint for users if no audio elements are found
        console.log(!newSpeakerState ? "Speaker Off: Audio elements not found. The setting will apply when audio becomes available." : "Speaker On: Audio elements not found. The setting will apply when audio becomes available.");
      } else {
        console.log(!newSpeakerState ? "Speaker Off: Speaker muted" : "Speaker On: Speaker unmuted");
      }

    } catch (err) {
      console.error("Error toggling speaker:", err);
      // Revert the state change if there was an error
      setCallState(s => ({ ...s, isSpeakerOn: !s.isSpeakerOn }));
    }
  };

  // Toggle device settings panel
  const toggleDeviceSettings = () => {
    setShowDeviceSettings(!showDeviceSettings);

    // If we're showing the settings and we're connected, refresh the device list
    if (!showDeviceSettings && callState.isConnected) {
      getAudioDevices();
      getCameraDevices();
    }
  };

  // Open speaker selection directly
  const openSpeakerSelection = () => {
    setShowDeviceSettings(true);

    // Refresh audio devices
    if (callState.isConnected) {
      getAudioDevices();
    }
  };

  // Quick switch between front and back camera (for mobile)
  const switchCamera = async () => {
    if (!deviceManagerRef.current || !callState.isConnected) return;

    try {
      const cameraDevices = await deviceManagerRef.current.getCameras();

      if (cameraDevices.length <= 1) {
        console.log("Info: Only one camera available");
        return;
      }

      // Find the current camera index
      const currentIndex = cameraDevices.findIndex((camera: any) => camera.id === selectedCameraId);

      // Get the next camera in the list (or loop back to the first one)
      const nextIndex = (currentIndex + 1) % cameraDevices.length;
      const nextCamera = cameraDevices[nextIndex];

      // Change to the next camera
      await changeCamera(nextCamera.id);

    } catch (err) {
      console.error("Error switching camera:", err);
    }
  };

  // TOUCH + MOUSE DRAG HANDLING
  const handleDragStart = (x: number, y: number) => {
    const startX = x;
    const startY = y;
    const startTop = localPosition.top;
    const startLeft = localPosition.left;

    const onMove = (moveX: number, moveY: number) => {
      const newTop = Math.min(
        Math.max(0, startTop + moveY - startY),
        window.innerHeight - 130
      );
      const newLeft = Math.min(
        Math.max(0, startLeft + moveX - startX),
        window.innerWidth - 88
      );
      setLocalPosition({ top: newTop, left: newLeft });
    };

    const onMouseMove = (e: MouseEvent) => onMove(e.clientX, e.clientY);
    const onTouchMove = (e: TouchEvent) => {
      const touch = e.touches[0];
      if (touch) onMove(touch.clientX, touch.clientY);
    };

    const endDrag = () => {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', endDrag);
      window.removeEventListener('touchmove', onTouchMove);
      window.removeEventListener('touchend', endDrag);
    };

    window.addEventListener('mousemove', onMouseMove);
    window.addEventListener('mouseup', endDrag);
    window.addEventListener('touchmove', onTouchMove);
    window.addEventListener('touchend', endDrag);
  };

  const getCallInfo = async (role = 'PATIENT') => {
    const {data} = await fetch(`**********************************/vcp/api/open-api/acs-details?role=${role}`, {
      method: 'GET',
      headers: {
        'moduletype': 'pura',
        'x-api-key': 'f5801458-7831-4d97-a9f1-553892e326e8',
      }
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .catch(error => {
        console.error('Fetch error:', error);
      });
      const formData = { userId: data.acsUserId, accessToken: data.token, groupId: data.acsRoomId};
      setFormData(formData);
      connectCall(formData);
  }

  const baseStyle =
    "w-14 h-14 rounded-full flex items-center justify-center transition-transform duration-200 border border-white";
  const activeStyle = "bg-[#0b2d57] text-white border border-[1px] border-[#FFFFFF1A]";
  const inactiveStyle = "bg-[#4a5568] text-white border border-[1px] border-white";
  return (
    <div className="relative login-form w-full h-screen bg-white overflow-hidden touch-none">

      {!callState.isConnected && (
        <div className="p-4 max-w-lg mx-auto space-y-12 pt-10">
      
          <Button className="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 w-full" onClick={() => getCallInfo('PATIENT')} disabled={callState.isConnecting}>
            {callState.isConnecting ? "Connecting..." : "Join As Patient"}
          </Button>

          <Button className="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 w-full" onClick={() => getCallInfo('DOCTOR')} disabled={callState.isConnecting}>
            {callState.isConnecting ? "Connecting..." : "Join As Doctor"}
          </Button>

        </div>
      )}

      {callState.isConnected && (
        <div ref={remoteVideoRef} className="remoteVideo absolute top-0 left-0 w-full h-full z-0 bg-black" />

      )}

      {/* Local Video Box */}
      {callState.isConnected && (
        <div
          ref={localVideoRef}
          className="localVideo absolute z-20 rounded-md bg-gray-800 touch-none"
          style={{
            width: '86px',
            height: '130px',
            top: localPosition.top,
            left: localPosition.left,
            cursor: 'grab'
          }}
          onMouseDown={e => handleDragStart(e.clientX, e.clientY)}
          onTouchStart={e => {
            const touch = e.touches[0];
            if (touch) handleDragStart(touch.clientX, touch.clientY);
          }}
        />
      )}

      {/* Camera Switch Button (only visible when connected and on mobile) */}
      {/* {callState.isConnected && callState.isCameraOn && (
        <Button
          variant="outline"
          size="sm"
          className="absolute top-24 right-4 z-30 bg-black bg-opacity-60 text-white border-gray-600 hover:bg-gray-700"
          onClick={switchCamera}
        >
          Switch Camera
        </Button>
      )} */}

      {/* Device Settings Panel */}
      {callState.isConnected && showDeviceSettings && (
        <div className="absolute bottom-24 left-1/2 transform -translate-x-1/2 z-40 p-4 bg-black bg-opacity-80 rounded-xl w-80">
          <h3 className="text-white text-lg font-semibold mb-3">Speaker Settings</h3>

          {/* Speaker Selection - Modified to allow only one speaker selection */}
          <div className="mb-4">
            <label className="text-white text-sm block mb-1">Select a speaker</label>
            <div className="space-y-2 max-h-40 overflow-y-auto bg-gray-800 p-3 rounded-md">
              {speakers.map(speaker => (
                <div key={speaker.id} className="flex items-center">
                  <input
                    type="radio"
                    id={`speaker-${speaker.id}`}
                    name="speaker-selection"
                    checked={selectedSpeakerId === speaker.id}
                    onChange={() => changeSpeaker(speaker.id)}
                    className="mr-2 h-5 w-5 accent-blue-500"
                  />
                  <label htmlFor={`speaker-${speaker.id}`} className="text-white text-sm">
                    {speaker.name}
                  </label>
                </div>
              ))}
              {speakers.length === 0 && (
                <div className="text-gray-400 text-sm p-2">No speakers found</div>
              )}
            </div>

            <div className="flex items-center mt-4 mb-2">
              <input
                type="checkbox"
                id="speaker-toggle"
                checked={callState.isSpeakerOn}
                onChange={toggleSpeaker}
                className="mr-2 h-5 w-5 accent-blue-500"
              />
              <label htmlFor="speaker-toggle" className="text-white text-sm font-medium">
                {callState.isSpeakerOn ? "Sound enabled" : "Sound disabled (muted)"}
              </label>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant="secondary"
              className="w-full"
              onClick={toggleDeviceSettings}
            >
              Close
            </Button>
          </div>
        </div>
      )}
      {callState.isConnected && (

        <div className="bg-[#031d3b] header text-white  flex items-center px-4 py-2 absolute left-0 top-0 w-full ">
          <button className="p-2 rounded hover:bg-blue-800">

            <i className='das das-left-icon text-2xl relative top-1'></i>
          </button>
          <div className="flex-1 text-left font-semibold text-lg">Dr. Muneeb Bhatti</div>
          <button className="p-2 rounded ">
            <i className='das das-video-switch text-2xl relative top-1' onClick={switchCamera}></i>
          </button>
        </div>
      )}
      {callState.isConnected && (
        <div className="absolute footer   transform  z-10 flex gap-3 px-4 py-2 bg-black bg-opacity-60 ">
          {/* <Button variant="secondary" onClick={toggleCamera}>
            {callState.isCameraOn ? "Camera Off" : "Camera On"}
          </Button>
        
          <Button variant="secondary" onClick={toggleMic}>
            {callState.isMicOn ? "Mute" : "Unmute"}
          </Button>
          <Button variant="secondary" onClick={toggleSpeaker}>
            {callState.isSpeakerOn ? "Speaker Off" : "Speaker On"}
          </Button>
          <Button variant="secondary" onClick={toggleDeviceSettings}>
            Settings
          </Button>
          <Button variant="secondary" onClick={disconnectCall}>
            Disconnect
          </Button> */}
          <div className="bg-[#061b38] py-4 flex justify-center gap-6">
            {/* Camera Toggle */}
            <button
              onClick={toggleCamera}
              className={`${baseStyle} ${callState.isCameraOn ? activeStyle : inactiveStyle}`}
              title={callState.isCameraOn ? "Camera On" : "Camera Off"}
            >
              {callState.isCameraOn ? (
                <i className="das das-video-call text-2xl" ></i>
              ) : (
                <i className="das das-video-call-cancel text-2xl" ></i>
              )}
            </button>

            {/* Mic Toggle */}
            <button
              onClick={toggleMic}
              className={`${baseStyle} ${callState.isMicOn ? activeStyle : inactiveStyle}`}
              title={callState.isMicOn ? "Mic On" : "Muted"}
            >
              {callState.isMicOn ? (
                <i className="das das-unmute text-2xl" ></i>
              ) : (
                <i className="das das-mute text-2xl" ></i>
              )}
            </button>

            {/* Speaker Toggle - Now opens speaker selection */}
            <button
              onClick={openSpeakerSelection}
              className={`${baseStyle} ${callState.isSpeakerOn ? activeStyle : inactiveStyle}`}
              title="Select Speakers"
            >
              <i className="das das-speaker text-2xl" ></i>
            </button>

            {/* Settings - commented out as requested */}
            {/* 
    <button
      onClick={toggleDeviceSettings}
      className={`${baseStyle} ${activeStyle}`}
      title="Settings"
    >
      <Cog6ToothIcon className="w-6 h-6" />
    </button>
    */}

            {/* Disconnect */}
            <button
              onClick={disconnectCall}
              className="w-14 h-14 rounded-full bg-red-600 text-white flex items-center justify-center hover:scale-110 transition-transform duration-200"
              title="End Call"
            >
              <i className="das das-call text-2xl" ></i>
            </button>
          </div>
        </div>
      )}
    </div>

  );
};

export default VideoCallApp;
